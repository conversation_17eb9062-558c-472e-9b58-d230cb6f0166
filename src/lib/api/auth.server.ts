import jwksClient from "jwks-rsa";
import jwt, { type GetPublicKeyOrSecret, type Secret } from "jsonwebtoken";
import { privateConfig } from "../../privateConfig.server.ts";
import { logger } from "$lib/logger/logger.svelte.ts";

type TokenCache = {
  token: string;
  expiresAt: number;
};

type JwtVerifyArgs = Parameters<typeof jwt.verify>;

const tokenCache: { [restaurantId: string]: TokenCache } = {};

export async function getOrCreateBridgeApiToken(restaurantId: string) {
  logger.info("getOrCreateBridgeApiToken");

  const now = Date.now();

  try {
    // If token is cached and not expired
    if (tokenCache[restaurantId] && tokenCache[restaurantId].expiresAt > now) {
      await verifyBridgeApiToken(tokenCache[restaurantId].token);
      return tokenCache[restaurantId].token;
    }
  } catch (err) {
    logger.warn("Failed to validate token", err);
  }

  const token = await generateBridgeApiToken();

  tokenCache[restaurantId] = {
    token,
    expiresAt: now + 86400 * 1000,
  };

  return token;
}

export async function generateBridgeApiToken() {
  logger.info("generateBridgeApiToken");
  const { authUrl, audienceUrl, clientId, clientSecret } = privateConfig;

  const fetchArguments =
    {
      authUrl,
      method: "POST",
      headers: {
        "content-type": "application/json",
      },
      body: JSON.stringify({
        client_id: clientId,
        client_secret: clientSecret,
        audience: audienceUrl,
        grant_type: "client_credentials",
      }),
    };

  logger.info(
    fetchArguments,
    "Generate bridge api fetch arguments: ",
  );

  const resp = await fetch(authUrl, {
    method: "POST",
    headers: {
      "content-type": "application/json",
    },
    body: JSON.stringify({
      client_id: clientId,
      client_secret: clientSecret,
      audience: audienceUrl,
      grant_type: "client_credentials",
    }),
  });

  if (!resp.ok) {
    throw new Error("Unauthenticated");
  }

  logger.info(resp, "Generate bridge api fetch response: ");
  const responseBody = await resp.json();

  logger.info(
    { bridgeApiAccessToken: responseBody.access_token },
    "Successfully parsed response json for bridge api token",
  );
  return responseBody.access_token;
}

export function jwtVerifyAsync(
  token: JwtVerifyArgs[0],
  secretOrPublicKey: JwtVerifyArgs[1],
): Promise<string | jwt.JwtPayload | undefined> {
  return new Promise((resolve, reject) => {
    jwt.verify(token, secretOrPublicKey, (err, decoded) => {
      if (err) {
        reject(err);
      }
      resolve(decoded);
    });
  });
}

export const verifyBridgeApiToken = async (token: string) => {
  const jwksUri = privateConfig.jwksUri;

  const client = jwksClient({
    jwksUri,
    requestHeaders: {},
    timeout: 30000,
  });

  const getKey: GetPublicKeyOrSecret = (header, getKeyCallback) => {
    client.getSigningKey(header.kid, (_, key) => {
      // @ts-expect-error These keys do exist on `key`. Not sure if we're missing some type narrowing.
      const signingKey = (key?.publicKey || key?.rsaPublicKey) as
        | Secret
        | undefined;
      getKeyCallback(null, signingKey);
    });
  };

  return jwtVerifyAsync(token, getKey);
};
