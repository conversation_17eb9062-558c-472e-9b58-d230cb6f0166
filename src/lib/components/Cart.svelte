<script lang="ts">
  import { goto } from "$app/navigation";
  import { getCartState } from "$lib/services/Cart.svelte";
  import { centToDollar } from "$lib/helpers/number.ts";
  import { routes } from "$lib/helpers/routes.ts";
  import CartItems from "./CartItems.svelte";
  import { getHealthCheckService } from "$lib/services/HealthCheckService.svelte.ts";
  import { page } from "$app/stores";

  const handleCheckout = () => {
    if (!$page.params.restaurant) return;
    goto(routes.checkout($page.params.restaurant));
  };

  const cart = getCartState();
  const healthCheckService = getHealthCheckService();
  const online = $derived(healthCheckService.online);
</script>

<section class="cart-container" aria-label="Cart">
  <CartItems />
  <section aria-label="Cart Footer" class="cart-footer">
    <div class="total">
      Subtotal: <span class="total-amount">{centToDollar(cart.subTotal)}</span>
    </div>
    <button
      class="button--primary-lg"
      disabled={!online}
      onclick={handleCheckout}
    >
      Checkout
    </button>
  </section>
</section>

<style>
  .cart-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .cart-footer {
    flex-shrink: 0;
    background-color: white;
    padding: var(--padding-sm);
    border-top: 1px solid var(--gray-200);
  }

  @media (min-width: 1024px) {
    .cart-container {
      height: calc(100vh - var(--min-header-height));
      overflow-y: scroll;
    }

    .cart-footer {
      position: fixed;
      bottom: 0;
      right: 0;
      left: calc(100vw - (var(--large-screen-cart-width) - 1px));
      padding: var(--padding-md);
    }
  }

  .total {
    font-weight: bold;
    font-size: 1.2em;
  }

  .total-amount {
    color: var(--primary-color);
  }

  .cart-footer button {
    width: 100%;
    padding: 10px;
    margin-top: 2px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
  }

  .cart-footer button:hover {
    background-color: var(--primary-color);
  }
</style>
