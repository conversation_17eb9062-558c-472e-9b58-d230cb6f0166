<script lang="ts">
  import Banner from "$lib/components/Banner.svelte";
  import { openDepartmentDrawer } from "$lib/components/CategoryDrawer.svelte";
  import DepartmentButton from "$lib/components/DepartmentButton.svelte";
  import CategoryDropdown from "$lib/components/Dropdown.svelte";
  import Search from "$lib/components/Search.svelte";
  import { getHealthCheckService } from "$lib/services/HealthCheckService.svelte.ts";
  import { getHeaderState } from "$lib/services/TopHeader.svelte";
  import type { BasicDepartment } from "$lib/types";
  import { slide } from "svelte/transition";
  import ListIcon from "~icons/mdi/format-list-bulleted";

  let {
    departments,
    searchTerm = $bindable(""),
    showSearch = $bindable(false),
  }: {
    departments: BasicDepartment[];
    searchTerm: string;
    showSearch: boolean;
  } = $props();

  const topHeader = getHeaderState();
  const healthCheckService = getHealthCheckService();

  let departmentListElement = $state<HTMLElement>();
  let horizontalScrollPosition = $state(0);
  let isUserScrolling = $state(false);
  let transitionDuration = $derived(isUserScrolling ? "0s" : "0.3s");
  let highlightedDepartment = $derived(
    topHeader ? topHeader.getTopHeader() : null,
  );
  let previousHighlightedDepartment = $state<string | null>(null);

  const calcScrollbarPosition = (
    highlightedDepartment: string | null,
    departmentList?: HTMLElement,
    currentHorizontalScrollPosition = 0,
  ) => {
    const DEFAULT_VALUES = {
      categoryUnderlineTransform: 0,
      categoryUnderlineWidth: 0,
    };

    if (!highlightedDepartment || !departmentList) return DEFAULT_VALUES;

    const departmentIndex = departments.findIndex(
      (dept) => dept.title === highlightedDepartment,
    );

    if (departmentIndex === -1) {
      return DEFAULT_VALUES;
    }

    const departmentElement = departmentList.children[
      departmentIndex
    ] as HTMLElement;

    if (departmentElement) {
      // Calculate the left offset of the department element and its width
      return {
        categoryUnderlineTransform:
          departmentElement.offsetLeft - currentHorizontalScrollPosition,
        categoryUnderlineWidth: departmentElement.offsetWidth,
      };
    }

    return DEFAULT_VALUES;
  };

  const handleHorizontalScroll = () => {
    if (departmentListElement) {
      isUserScrolling = true;
      horizontalScrollPosition = departmentListElement.scrollLeft;
    }
  };

  /**
   * Scrolling into view when the highlighted department changes
   * And flipping isUserScrolling to false when the user is done scrolling
   * */
  $effect(() => {
    if (highlightedDepartment !== previousHighlightedDepartment) {
      // Find the current department element
      const departmentIndex = departments.findIndex(
        (dept) => dept.title === highlightedDepartment,
      );

      if (departmentIndex >= 0 && departmentListElement) {
        const departmentElement =
          departmentListElement.children[departmentIndex];
        if (departmentElement) {
          departmentElement.scrollIntoView({
            behavior: "smooth",
            block: "nearest",
            inline: "center",
          });
        }
      }
      previousHighlightedDepartment = highlightedDepartment;
    }
    isUserScrolling = false;
  });

  const { categoryUnderlineTransform, categoryUnderlineWidth } = $derived(
    calcScrollbarPosition(
      highlightedDepartment,
      departmentListElement,
      horizontalScrollPosition,
    ),
  );
</script>

{#if !healthCheckService.online}
  <div transition:slide>
    <Banner type="offline" />
  </div>
{/if}
{#if healthCheckService.closingSoon && healthCheckService.timeUntilClosing}
  <div transition:slide>
    <Banner
      type="closing-soon"
      timeUntilClosingInMs={healthCheckService.timeUntilClosing}
    />
  </div>
{/if}
<div class="title-wrapper">
  <h1>Categories</h1>
  <Search bind:searchTerm bind:showSearch />
</div>
<div class="department-list-wrapper">
  <div class="department-list-scroll-container">
    <div
      class="department-list"
      bind:this={departmentListElement}
      onscroll={handleHorizontalScroll}
    >
      {#each departments as department}
        <DepartmentButton name={department.title} />
      {/each}
    </div>
    <div
      class="category-underline"
      style="
        transform: translateX({categoryUnderlineTransform}px); 
        width: {categoryUnderlineWidth}px;
        transition-duration: {transitionDuration};"
    ></div>
    <div class="department-list-scroll-gradient"></div>
  </div>
  <div class="mobile-view">
    <button onclick={openDepartmentDrawer} class="dropdown">
      <ListIcon font-size={17} />
      <span class="sr-only">Open Categories</span>
    </button>
  </div>
  <div class="desktop-only">
    <CategoryDropdown items={departments} />
  </div>
</div>

<style>
  .category-underline {
    position: absolute;
    bottom: 0;
    left: 0;
    transform-origin: left;
    transition: transform 0.15s ease-in-out;
    border-radius: var(--radius-sm) var(--radius-sm) 0 0;
    background-color: var(--primary-color);
    height: 4px;
  }

  .department-list-wrapper {
    --scroll-gradient-size: 2rem;
    display: flex;
    position: relative;
    align-items: center;
  }

  .department-list-scroll-container {
    position: relative;
    overflow: hidden;
  }

  .department-list {
    display: flex;
    gap: var(--gap-md);
    padding-inline-start: var(--padding-md);
    padding-inline-end: var(--scroll-gradient-size);
    padding: var(--padding-xs);
    overflow-x: auto;
    scrollbar-width: none;
  }

  .department-list::-webkit-scrollbar {
    display: none;
  }

  .department-list-scroll-gradient {
    position: absolute;
    inset-block: 0;
    inset-inline-end: 0;
    background-image: linear-gradient(
      90deg,
      transparent,
      var(--main-bg-color) 90%
    );
    width: var(--scroll-gradient-size);
    height: 80%;
    pointer-events: none;
  }

  .dropdown {
    background-color: transparent;
  }

  .title-wrapper {
    display: flex;
    align-items: center;
    gap: var(--gap-sm);
  }

  h1 {
    padding: var(--padding-sm) var(--padding-md);
  }

  @media (max-width: 768px) {
    h1 {
      padding: var(--padding-sm);
    }

    .title-wrapper {
      gap: var(--gap-md);
    }
  }
</style>
