import type { DetailedItem } from "$lib/types";
import { getModifierListState } from "$lib/components/Modifier/modifier-list-state.svelte.ts";

export const shouldRenderChildrenAsLabels = (
  modifier: DetailedItem,
  modifierIsLabel: boolean,
) => {
  if (!modifier.multiModLists || modifierIsLabel) {
    return false;
  }

  return modifier.modifiers.length > 0;
};

export const getSelectedChildren = (modifier: DetailedItem): DetailedItem[] =>
  modifier.modifiers?.filter((m) => m.selected) ?? [];

export const hasChildrenAndIsSelected = (modifier: DetailedItem) =>
  modifier.modifiers.length > 0 && modifier.selected;

export const allChildrenResolved = (modifier: DetailedItem) => {
  const modifierListState = getModifierListState();
  const validationStates = modifierListState.validationStates;

  return modifier.modifiers.every(
    (child) =>
      /* !child.selected || */ validationStates[child.item] !== "invalid",
  );
};

export const getValidationState = (
  modifier: DetailedItem,
  renderAsInput: boolean,
): "" | "valid" | "invalid" => {
  // Unselected checkboxes are automatically resolved (this is redundant to `shouldBeValidated` but is here for safety and clarity)
  if (renderAsInput && !modifier.selected) {
    return "";
  }

  // If this modifier is a selected checkbox with no children, it's resolved
  if (!modifier.modifiers) {
    return "";
  }

  const selectedChildren = getSelectedChildren(modifier);

  const minimumSatisfied = selectedChildren.length >= modifier.modMinSel;
  // 0 is unlimited
  const maximumSatisfied =
    modifier.modMaxSel === 0
      ? true
      : selectedChildren.length <= modifier.modMaxSel;

  if (!minimumSatisfied || !maximumSatisfied) {
    return "invalid";
  }

  // If minimum and maximum are satisfied, and every child is resolved, return true

  return allChildrenResolved(modifier) ? "valid" : "invalid";
};

export const hasIncompleteRequiredChildSelections = (modifier: DetailedItem) =>
  modifier.selected &&
  modifier.modifiers.some((child) => child.modMinSel > 0 && !child.selected);
