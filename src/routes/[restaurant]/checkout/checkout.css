.tip-section-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  width: 100%;
}

.tip-grouper {
  display: flex;
  flex-wrap: wrap;
}

.tip-grouper input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.tip-selection {
  display: flex;
  padding: var(--padding-sm) var(--padding-md);
  border: 1px solid var(--gray-300);
  background-color: white;
  color: var(--text-color);
  transition: all 0.2s ease-in-out;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.tip-selection:first-of-type {
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
}

.tip-selection:last-of-type {
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
}

.tip-selection:hover {
  border-color: var(--primary-color);
}

input[type="radio"]:checked + .tip-selection {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

@media (max-width: 460px) {
  .tip-selection {
    padding: var(--padding-sm);
    font-size: var(--font-sm);
  }
}

.tip-amount {
  color: var(--green-500);
}

.back-button {
  height: 50px;
  width: 200px;
  margin-bottom: var(--margin-md);
  border-radius: var(--radius-md);
  background-color: transparent;
}

.back-button-content {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
}

.extra-fees {
  display: flex;
  align-items: center;
}

.info-icon {
  color: var(--gray-900);
  margin-left: var(--margin-sm);
  font-size: var(--font-xs);
}

.checkout-page {
  max-width: 700px;
  margin: 0 auto;
  padding: var(--padding-lg);
  background-color: var(--gray-50);
}

h1 {
  margin-bottom: 2rem;
}

h2 {
  font-size: var(--font-lg);
  font-weight: 600;
  margin-bottom: 1rem;
}

h3 {
  font-size: var(--font-md);
  font-weight: 600;
  margin-bottom: 1rem;
}

section {
  background-color: white;
  padding: var(--padding-lg);
  margin-bottom: 1.5rem;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-elevation-medium);
  border: 1px solid var(--gray-200);
}

.pickup-info {
  display: grid;
  grid-template-columns: 2fr 2fr;
  margin-block-end: var(--margin-md);
  align-items: start;
  gap: var(--padding-md);
}

@media (max-width: 768px) {
  .pickup-info {
    grid-template-columns: 1fr;
  }
}

.personal-info {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--margin-sm);
}

/** TODO: Add payment options when ready */

/* .payment-options {
  display: flex;
  flex-direction: column;
  gap: var(--padding-md);
}

.payment-option {
  display: flex;
  align-items: center;
  justify-items: stretch;
  padding: var(--padding-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.2s ease-in-out;
}

.payment-option:hover {
  border-color: var(--primary-color);
}

.payment-option input[type="radio"] {
  margin-right: 1rem;
}

.card-icon,
.apple-icon,
.gpay-icon {
  color: var(--gray-500);
  margin-left: auto;
}

.payment-option input[type="radio"] {
  width: auto;
}

*/

.card-form {
  display: flex;
  flex-direction: column;
  gap: var(--padding-sm);
}

.personal-info label {
  margin-top: var(--padding-sm);
}

.personal-info input {
  margin-bottom: var(--padding-sm);
}

.card-details {
  display: flex;
  gap: 1rem;
}

.order-overview {
  display: flex;
  flex-direction: column;
  gap: var(--padding-md);
}

.amount {
  display: flex;
  justify-content: space-between;
}

.input-group-wrapper {
  display: flex;
  gap: 1rem;
}

.input-group {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.icon-cc-wrapper {
  max-width: 295px;
}

.input-wrapper {
  display: flex;
  min-height: 50px;
  width: 100%;
}

#ccNumber {
  flex-grow: 1;
  width: 100%;
}

.icon {
  display: flex;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  padding: var(--padding-sm);
  border: 1px solid var(--gray-300);
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
  border-right: none;
}

.checkout-button {
  display: block;
  width: 100%;
  padding: var(--padding-md);
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-lg);
  transition: background-color 0.2s ease-in-out;
}

.checkout-button:hover {
  background-color: var(--primary-color-hover);
}

.form-skeleton-wrapper {
  position: relative;
}

.required-indicator {
  color: red;
}

.main-skeleton {
  position: absolute;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  transition: opacity 0.2s ease-out;
  background-color: white;
  padding: var(--padding-md);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-elevation-medium);
  border: 1px solid var(--gray-200);
}

.main-skeleton.hidden {
  opacity: 0;
  pointer-events: none;
}

.skeleton-title {
  height: 1rem;
  width: 40%;
  background: linear-gradient(
    to right,
    var(--gray-200) 8%,
    var(--gray-300) 18%,
    var(--gray-200) 33%
  );
  background-size: 800px 104px;
  border-radius: var(--radius-sm);
  animation: shine 1.5s linear infinite;
  margin-bottom: var(--margin-md);
}

.skeleton-input {
  height: 40px;
  width: 100%;
  background: linear-gradient(
    to right,
    var(--gray-200) 8%,
    var(--gray-300) 18%,
    var(--gray-200) 33%
  );
  background-size: 800px 104px;
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
  animation: shine 1.5s linear infinite;
}

.skeleton-group {
  display: flex;
  gap: var(--gap-xs);
  margin-bottom: var(--margin-md);
}

.small-skeleton {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.skeleton-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(
    to right,
    var(--gray-200) 8%,
    var(--gray-300) 18%,
    var(--gray-200) 33%
  );
  background-size: 800px 104px;
  border-radius: var(--radius-md) 0 0 var(--radius-md);
  animation: shine 1.5s linear infinite;
  flex-shrink: 0;
}

.skeleton-label {
  height: 1rem;
  width: 30%;
  background: linear-gradient(
    to right,
    var(--gray-200) 8%,
    var(--gray-300) 18%,
    var(--gray-200) 33%
  );
  background-size: 800px 104px;
  border-radius: var(--radius-sm);
  animation: shine 1.5s linear infinite;
  margin-bottom: var(--margin-sm);
}

@keyframes shine {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.extra-fees-content {
  display: flex;
  flex-direction: column;
  gap: var(--padding-md);
}

.extra-fees-amount {
  font-size: var(--font-lg);
  font-weight: 600;
}

.extra-fees-description {
  color: var(--gray-600);
  margin-top: var(--margin-sm);
}

.pickup-time {
  font-size: var(--font-md);
  color: var(--gray-800);
}

.amount-placeholder {
  display: flex;
  justify-content: space-between;
  opacity: 0.6;
}

.calculating {
  font-style: italic;
  color: var(--gray-800);
}

.warning-section {
  display: flex;
  align-items: flex-start;
  gap: var(--padding-md);
}

.warning-icon {
  color: var(--blue-600);
  margin-top: 2px;
  font-size: var(--font-lg);
}
