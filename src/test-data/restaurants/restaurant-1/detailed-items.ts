import { genUuid } from "$lib/helpers/uuid";
import type { DetailedItem } from "$lib/types";

export const detailedItems: DetailedItem[] = [
  {
    item: "12351",
    desc: "Item with no modifiers",
    detailedDesc: "Item with no modifiers",
    price: 0,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12355",
    desc: "Out of stock item",
    detailedDesc: "Out of stock item",
    price: 25,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12352",
    desc: "Cheeseburger",
    detailedDesc:
      "Juicy beef patty topped with melted cheese, served with fries.",
    price: 999,
    multiModLists: true,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    isVisible: true,
    modifierDesc:
      "Juicy beef patty topped with melted cheese, served with fries.",
    modifiers: [
      {
        item: genUuid(),
        desc: "Cheese Options",
        price: 0,
        multiModLists: false,
        modMaxSel: 1,
        modMinSel: 1,
        count: 17,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            desc: "American Cheese",
            price: 199,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Cheddar Cheese",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Swiss Cheese",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "No Cheese",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Extra Cheese",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [
              {
                item: genUuid(),
                desc: "Double Cheese",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
              {
                item: genUuid(),
                desc: "Triple Cheese",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
            ],
          },
        ],
      },
      {
        item: genUuid(),
        desc: "Bun Type",
        price: 0,
        multiModLists: false,
        modMaxSel: 1,
        modMinSel: 1,
        count: 17,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            desc: "Sesame Seed Bun",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Brioche Bun",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Gluten-Free Bun",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "No Bun (Lettuce Wrap)",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
        ],
      },
      {
        item: genUuid(),
        desc: "Patty Options",
        multiModLists: false,
        count: 17,
        selected: false,
        qty: 0,
        isVisible: true,
        price: 0,
        modMinSel: 1,
        modMaxSel: 1,
        modifiers: [
          {
            item: genUuid(),
            desc: "Beef Patty",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Chicken Patty",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Veggie Patty",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Extra Patty",
            price: 0,
            multiModLists: false,
            count: 17,
            selected: false,
            qty: 0,
            isVisible: true,
            modMinSel: 1,
            modMaxSel: 1,
            modifiers: [
              {
                item: genUuid(),
                desc: "Double Beef Patty",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
              {
                item: genUuid(),
                desc: "Double Chicken Patty",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
            ],
          },
        ],
      },
      {
        item: genUuid(),
        desc: "Toppings",
        price: 0,
        multiModLists: false,
        count: 17,
        selected: false,
        qty: 0,
        isVisible: true,
        modMinSel: 0,
        modMaxSel: 5,
        modifiers: [
          {
            item: genUuid(),
            desc: "Lettuce",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Tomato",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Onions",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Pickles",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Premium Toppings",
            isVisible: true,
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [
              {
                item: genUuid(),
                desc: "Avocado",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
              {
                item: genUuid(),
                desc: "Fried Egg",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
              {
                item: genUuid(),
                desc: "Caramelized Onions",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
              {
                item: genUuid(),
                desc: "Bacon",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
            ],
          },
        ],
      },
      {
        item: genUuid(),
        desc: "Sauces",
        price: 0,
        multiModLists: false,
        count: 17,
        selected: false,
        qty: 0,
        isVisible: true,
        modMinSel: 0,
        modMaxSel: 3,
        modifiers: [
          {
            item: genUuid(),
            desc: "Ketchup",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Mustard",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Mayonnaise",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "BBQ Sauce",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Special Sauce",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Spicy Sauces",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [
              {
                item: genUuid(),
                desc: "Sriracha",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
              {
                item: genUuid(),
                desc: "Hot Sauce",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
              {
                item: genUuid(),
                desc: "Chipotle Mayo",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
            ],
          },
        ],
      },
      {
        item: genUuid(),
        desc: "Side Options",
        price: 0,
        multiModLists: false,
        count: 17,
        selected: false,
        qty: 0,
        isVisible: true,
        modMinSel: 1,
        modMaxSel: 1,
        modifiers: [
          {
            item: genUuid(),
            desc: "French Fries",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Sweet Potato Fries",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Onion Rings",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Salad Options",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [
              {
                item: genUuid(),
                desc: "Caesar Salad",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
              {
                item: genUuid(),
                desc: "Garden Salad",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
              {
                item: genUuid(),
                desc: "Salad Dressing",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                isVisible: true,
                modifiers: [
                  {
                    item: genUuid(),
                    desc: "Ranch",
                    price: 0,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: 17,
                    selected: false,
                    qty: 0,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: genUuid(),
                    desc: "Italian",
                    price: 0,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: 17,
                    selected: false,
                    qty: 0,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: genUuid(),
                    desc: "Balsamic Vinaigrette",
                    price: 0,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: 17,
                    selected: false,
                    qty: 0,
                    modifiers: [],
                    isVisible: true,
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        item: genUuid(),
        desc: "Cooking Preferences",
        price: 0,
        multiModLists: false,
        count: 17,
        selected: false,
        qty: 0,
        isVisible: true,
        modMinSel: 1,
        modMaxSel: 1,
        modifiers: [
          {
            item: genUuid(),
            desc: "Rare",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Medium Rare",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Medium",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Well Done",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
        ],
      },
      {
        item: genUuid(),
        desc: "Meal Add-ons",
        price: 0,
        multiModLists: false,
        count: 17,
        selected: false,
        qty: 0,
        isVisible: true,
        modMinSel: 0,
        modMaxSel: 3,
        modifiers: [
          {
            item: genUuid(),
            desc: "Extra Fries",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Side of Mac & Cheese",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Extra Bacon",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Drink",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [
              {
                item: genUuid(),
                desc: "Soda",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [
                  {
                    item: genUuid(),
                    desc: "Pepsi",
                    price: 0,
                    count: 17,
                    selected: false,
                    qty: 0,
                    modifiers: [],
                    isVisible: true,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                  },
                  {
                    item: genUuid(),
                    desc: "Coke",
                    price: 0,
                    count: 17,
                    selected: false,
                    qty: 0,
                    modifiers: [],
                    isVisible: true,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                  },
                  {
                    item: genUuid(),
                    desc: "Sprite",
                    price: 0,
                    count: 17,
                    selected: false,
                    qty: 0,
                    modifiers: [],
                    isVisible: true,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                  },
                ],
                isVisible: true,
              },
              {
                item: genUuid(),
                desc: "Iced Tea",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
              {
                item: genUuid(),
                desc: "Lemonade",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
              {
                item: genUuid(),
                desc: "Water",
                price: 0,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: 17,
                selected: false,
                qty: 0,
                modifiers: [],
                isVisible: true,
              },
            ],
          },
          {
            item: genUuid(),
            desc: "Side Salad",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
        ],
      },
    ],
  },
  {
    item: "12353",
    desc: "Margherita Pizza",
    detailedDesc:
      "Classic pizza topped with fresh mozzarella, tomatoes, and basil.",
    price: 1299,
    multiModLists: true,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    isVisible: true,
    modifierDesc:
      "Classic pizza topped with fresh mozzarella, tomatoes, and basil.",
    modifiers: [
      {
        item: genUuid(),
        desc: "Pizza Size",
        price: 0,
        multiModLists: false,
        count: 17,
        selected: false,
        qty: 0,
        isVisible: true,
        modMinSel: 1,
        modMaxSel: 1,
        modifiers: [
          {
            item: genUuid(),
            desc: "Small",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Medium",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Large",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
        ],
      },
      {
        item: genUuid(),
        desc: "Crust Type",
        price: 0,
        multiModLists: false,

        count: 17,
        selected: false,
        qty: 0,
        isVisible: true,
        modMinSel: 1,
        modMaxSel: 1,
        modifiers: [
          {
            item: genUuid(),
            desc: "Thin Crust",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Thick Crust",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
        ],
      },
      {
        item: genUuid(),
        desc: "Extra Toppings",
        price: 0,
        multiModLists: false,
        count: 17,
        selected: false,
        qty: 0,
        isVisible: true,
        modMinSel: 0,
        modMaxSel: 5,
        modifiers: [
          {
            item: genUuid(),
            desc: "Pepperoni",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Mushrooms",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Olives",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Extra Cheese",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Pineapple",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
        ],
      },
    ],
  },
  {
    item: "12354",
    desc: "Steak Frites",
    detailedDesc:
      "Grilled steak served with French fries and a side of herb butter.",
    price: 1999,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: true,
    isVisible: true,
    modifierDesc:
      "Grilled steak served with French fries and a side of herb butter.",
    modifiers: [
      {
        item: genUuid(),
        desc: "Steak Doneness",
        price: 0,
        multiModLists: false,
        count: 17,
        selected: false,
        qty: 0,
        isVisible: true,
        modMinSel: 1,
        modMaxSel: 1,
        modifiers: [
          {
            item: genUuid(),
            desc: "Rare",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Medium Rare",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Medium",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Well Done",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
        ],
      },
      {
        item: genUuid(),
        desc: "Extra Sauces",
        price: 0,
        multiModLists: false,

        count: 17,
        selected: false,
        qty: 0,
        isVisible: true,
        modMinSel: 0,
        modMaxSel: 2,
        modifiers: [
          {
            item: genUuid(),
            desc: "Peppercorn Sauce",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
          {
            item: genUuid(),
            desc: "Garlic Butter",
            price: 0,
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: 17,
            selected: false,
            qty: 0,
            modifiers: [],
            isVisible: true,
          },
        ],
      },
    ],
  },
  {
    item: "12345",
    price: 999,
    desc: "Burger",
    detailedDesc: "Burger",
    modifierDesc: "Select cheese and bun options",
    multiModLists: true,
    modMaxSel: 2,
    modMinSel: 1,
    count: 1,
    selected: false,
    qty: 1,
    isVisible: true,
    modifiers: [
      {
        item: genUuid(),
        price: 0,
        desc: "Cheese Options",
        multiModLists: false,
        modMaxSel: 1,
        modMinSel: 1,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 199,
            desc: "American Cheese",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Cheddar Cheese",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Swiss Cheese",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "No Cheese",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
      {
        item: genUuid(),
        price: 0,
        desc: "Bun Type",
        multiModLists: false,
        modMaxSel: 1,
        modMinSel: 1,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 0,
            desc: "Sesame Seed Bun",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Brioche Bun",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Gluten-Free Bun",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "No Bun (Lettuce Wrap)",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
    ],
  },
  {
    item: "12346",
    price: 1299,
    desc: "Classic pizza",
    detailedDesc: "A classic pizza with your choice of toppings.",
    modifierDesc: "Choose size, crust, and extra toppings",
    multiModLists: true,
    modMaxSel: 3,
    modMinSel: 1,
    count: 1,
    selected: false,
    qty: 1,
    isVisible: true,
    modifiers: [
      {
        item: genUuid(),
        price: 0,
        desc: "Pizza Size",
        multiModLists: false,
        modMaxSel: 1,
        modMinSel: 1,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 0,
            desc: "Small",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Medium",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Large",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
      {
        item: genUuid(),
        price: 0,
        desc: "Crust Type",
        multiModLists: false,
        modMaxSel: 1,
        modMinSel: 1,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 0,
            desc: "Thin Crust",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Thick Crust",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
      {
        item: genUuid(),
        price: 0,
        desc: "Extra Toppings",
        multiModLists: false,
        modMaxSel: 2,
        modMinSel: 0,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 0,
            desc: "Pepperoni",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Mushrooms",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Olives",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Extra Cheese",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Pineapple",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
    ],
  },
  {
    item: "12347",
    price: 1599,
    desc: "Grilled chicken breast",
    detailedDesc: "Grilled chicken breast served with a side of vegetables.",
    modifierDesc: "Choose your sauce and side",
    multiModLists: true,
    modMaxSel: 2,
    modMinSel: 0,
    count: 1,
    selected: false,
    qty: 1,
    isVisible: true,
    modifiers: [
      {
        item: genUuid(),
        price: 0,
        desc: "Sauce",
        multiModLists: false,
        modMaxSel: 1,
        modMinSel: 0,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 0,
            desc: "BBQ Sauce",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Teriyaki Sauce",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "No Sauce",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
      {
        item: genUuid(),
        price: 0,
        desc: "Side",
        multiModLists: false,
        modMaxSel: 1,
        modMinSel: 0,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 0,
            desc: "French Fries",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Mashed Potatoes",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Steamed Veggies",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Side Salad",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
    ],
  },
  {
    item: "12348",
    price: 899,
    desc: "Caesar Salad",
    detailedDesc:
      "Crisp romaine lettuce tossed in Caesar dressing with croutons.",
    modifierDesc: "Add extras or adjust dressing",
    multiModLists: true,
    modMaxSel: 2,
    modMinSel: 0,
    count: -1,
    selected: false,
    qty: 1,
    isVisible: true,
    modifiers: [
      {
        item: genUuid(),
        price: 0,
        desc: "Dressing Preference",
        multiModLists: false,
        modMaxSel: 1,
        modMinSel: 0,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 0,
            desc: "Extra Dressing",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Light Dressing",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "On The Side",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
      {
        item: genUuid(),
        price: 0,
        desc: "Extra Toppings",
        multiModLists: false,
        modMaxSel: 2,
        modMinSel: 0,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 0,
            desc: "Grilled Chicken",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Shrimp",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Bacon Bits",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 0,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Extra Parmesan",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
    ],
  },
  {
    item: "12349",
    price: 1199,
    desc: "Beer-battered fish fillet",
    detailedDesc:
      "Beer-battered fish fillet served with tartar sauce and lemon.",
    modifierDesc: "Choose your sauce and side",
    multiModLists: true,
    modMaxSel: 2,
    modMinSel: 0,
    count: 1,
    selected: false,
    qty: 1,
    isVisible: true,
    modifiers: [
      {
        item: genUuid(),
        price: 0,
        desc: "Sauce Options",
        multiModLists: false,
        modMaxSel: 1,
        modMinSel: 0,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 0,
            desc: "Tartar Sauce",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Lemon Wedge",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "No Sauce",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
      {
        item: genUuid(),
        price: 0,
        desc: "Side Options",
        multiModLists: false,
        modMaxSel: 1,
        modMinSel: 0,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 0,
            desc: "Chips",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Coleslaw",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Side Salad",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
    ],
  },
  {
    item: "12350",
    price: 1999,
    desc: "Grilled steak",
    detailedDesc: "Juicy grilled steak cooked to your preference.",
    modifierDesc: "Pick your steak doneness and a side",
    multiModLists: true,
    modMaxSel: 2,
    modMinSel: 0,
    count: 1,
    selected: false,
    qty: 1,
    isVisible: true,
    modifiers: [
      {
        item: genUuid(),
        price: 0,
        desc: "Steak Doneness",
        multiModLists: false,
        modMaxSel: 1,
        modMinSel: 0,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 0,
            desc: "Rare",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Medium Rare",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Medium",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Medium Well",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Well Done",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
      {
        item: genUuid(),
        price: 0,
        desc: "Side Options",
        multiModLists: false,
        modMaxSel: 1,
        modMinSel: 0,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 0,
            desc: "French Fries",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Mashed Potatoes",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Steamed Veggies",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "House Salad",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
    ],
  },
  {
    item: "12344",
    desc: "Chicken Alfredo",
    detailedDesc: "Creamy Alfredo sauce with grilled chicken over pasta.",
    price: 1500,
    modMaxSel: 0,
    modMinSel: 0,
    count: 1,
    isVisible: true,
    multiModLists: false,
    qty: 0,
    selected: false,
    modifierDesc:
      "Grilled chicken breast served over fettuccine pasta with creamy Alfredo sauce.",
    modifiers: [
      {
        item: genUuid(),
        desc: "Fettuccine",
        price: 0,
        multiModLists: false,
        modMaxSel: 0,
        modMinSel: 0,
        count: 17,
        selected: false,
        qty: 0,
        modifiers: [],
        isVisible: true,
      },
      {
        item: genUuid(),
        desc: "Penne",
        price: 0,
        multiModLists: false,
        modMaxSel: 0,
        modMinSel: 0,
        count: 17,
        selected: false,
        qty: 0,
        modifiers: [],
        isVisible: true,
      },
      {
        item: genUuid(),
        desc: "Gluten-Free",
        price: 0,
        multiModLists: false,
        modMaxSel: 0,
        modMinSel: 0,
        count: 17,
        selected: false,
        qty: 0,
        modifiers: [],
        isVisible: true,
      },
    ],
  },
  {
    item: "43a223cf-d21d-4080-8721-362dd96ae562",
    price: 0,
    desc: "Deeply nested item with no labels",
    detailedDesc: "Deeply nested item with no labels",
    modifierDesc: "",
    multiModLists: false,
    modMaxSel: 0,
    modMinSel: 0,
    count: -1,
    selected: false,
    qty: 1,
    modifiers: [
      {
        item: "da5020ba-1e71-47cc-82ad-f2fc656ba457",
        price: 700,
        desc: "Doub Captn",
        modifierDesc: null,
        multiModLists: true,
        modMaxSel: 1,
        modMinSel: 1,
        count: -1,
        selected: false,
        qty: 1,
        modifiers: [
          {
            item: "78d94497-c27b-472d-896e-8077138172dc",
            price: 0,
            desc: "Vod List 2",
            modifierDesc: null,
            multiModLists: false,
            modMaxSel: 2,
            modMinSel: 1,
            count: -1,
            selected: false,
            qty: 1,
            modifiers: [
              {
                item: "f1a7f6ec-d707-4828-9e92-ce6147e22cc1",
                price: 30,
                desc: "Tall Gls",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "e927cfd2-046d-4a01-bcc9-b2bdd7e50483",
                price: 0,
                desc: "Rocks",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "2075e643-c0df-4040-b872-b9bcbe82fab9",
                price: 0,
                desc: "Up",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "73979b27-662a-4ec4-b164-3a906b3268f4",
                price: 0,
                desc: "Pop",
                modifierDesc: null,
                multiModLists: true,
                modMaxSel: 1,
                modMinSel: 1,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [
                  {
                    item: "be45d2c3-2ed0-4792-9fea-72d970d72fab",
                    price: 0,
                    desc: "Pop Type",
                    modifierDesc: "",
                    multiModLists: false,
                    modMaxSel: 1,
                    modMinSel: 1,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [
                      {
                        item: "f7c7d7cb-a010-4056-8938-2d757c187eb5",
                        price: 25,
                        desc: "Tea",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "2ed9d148-9eb2-475a-9bce-ad928704a900",
                        price: 25,
                        desc: "Lemade",
                        modifierDesc: "",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "4fbeeb2f-daa6-4bf2-a645-524a2851d8fb",
                        price: 200,
                        desc: "Red Bull",
                        modifierDesc: "",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "c69ec118-6b6a-43d0-9a95-fa379fc511c2",
                        price: 25,
                        desc: "coke",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "fe064523-1381-451d-8118-cad4a78f88cb",
                        price: 25,
                        desc: "Diet",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "edea9748-8e4e-4ab2-82b0-161e7ba98fee",
                        price: 50,
                        desc: "sours",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "c4d45708-ea4a-4ffa-8819-57ef67b8dc0c",
                        price: 25,
                        desc: "Soda",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "b9dbd04d-1be9-499f-a55c-71c505c684fb",
                        price: 25,
                        desc: "Tonic",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "54ff7204-2d54-48a2-8c00-cb3578176448",
                        price: 0,
                        desc: "Water",
                        modifierDesc: "",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "5e00f167-b5ac-4df7-a298-3f59bc132c97",
                        price: 25,
                        desc: "Ginger",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "b759ec9a-7182-40e5-8a61-e4014e98c2ea",
                        price: 25,
                        desc: "sprite",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "6694998f-7747-436a-9464-5d4adeaf58ac",
                        price: 25,
                        desc: "dr. pep",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                    ],
                    isVisible: false,
                  },
                ],
                isVisible: false,
              },
              {
                item: "30d9e07f-b106-4e72-aec8-1fac28353508",
                price: 0,
                desc: "Juice",
                modifierDesc: null,
                multiModLists: true,
                modMaxSel: 1,
                modMinSel: 1,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [
                  {
                    item: "bf388cc2-52d8-4de7-aeef-c4eeab6aba8e",
                    price: 0,
                    desc: "Juice Type",
                    modifierDesc: null,
                    multiModLists: false,
                    modMaxSel: 1,
                    modMinSel: 1,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [
                      {
                        item: "2a0bc524-6fb7-46e0-9036-e39954bd5897",
                        price: 50,
                        desc: "OJ",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "15b81bcb-82e0-4f8d-8824-f24a98a0b6cc",
                        price: 50,
                        desc: "G F",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "7061103b-f473-441d-aea7-188afce9f8fe",
                        price: 50,
                        desc: "Cranbrry",
                        modifierDesc: "",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "a2d795f7-7008-4b67-bafe-2b6e62ed1a95",
                        price: 50,
                        desc: "Pineapple",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "d1b2000e-bf9c-4c82-8729-49b3a9d44cc2",
                        price: 50,
                        desc: "Tomato",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                    ],
                    isVisible: false,
                  },
                ],
                isVisible: false,
              },
            ],
            isVisible: false,
          },
        ],
        isVisible: true,
      },
    ],
    isVisible: false,
  },
  // Add detailed items for the new basic items added previously
  {
    item: "12356", // Chocolate Cake
    desc: "Chocolate Cake",
    detailedDesc: "Rich and decadent chocolate cake.",
    price: 799,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12357", // Tiramisu
    desc: "Tiramisu",
    detailedDesc: "Classic Italian dessert with coffee-soaked ladyfingers.",
    price: 699,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12358", // Cheesecake
    desc: "Cheesecake",
    detailedDesc:
      "Creamy New York style cheesecake with a graham cracker crust.",
    price: 899,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12359", // Soda
    desc: "Soda",
    detailedDesc: "Your choice of refreshing soda.",
    price: 299,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12360", // Iced Tea
    desc: "Iced Tea",
    detailedDesc: "Freshly brewed iced tea, sweetened or unsweetened.",
    price: 399,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12361", // Coffee
    desc: "Coffee",
    detailedDesc: "Hot brewed coffee.",
    price: 499,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12362", // Tomato Soup
    desc: "Tomato Soup",
    detailedDesc: "Creamy tomato soup, perfect for a chilly day.",
    price: 699,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12363", // Chicken Noodle Soup
    desc: "Chicken Noodle Soup",
    detailedDesc: "Hearty chicken noodle soup with vegetables.",
    price: 799,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12364", // Clam Chowder
    desc: "Clam Chowder",
    detailedDesc: "New England style clam chowder.",
    price: 899,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12365", // Mozzarella Sticks
    desc: "Mozzarella Sticks",
    detailedDesc: "Crispy fried mozzarella sticks served with marinara sauce.",
    price: 1099,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12366", // Pepperoni Pizza
    desc: "Pepperoni Pizza",
    detailedDesc: "Classic pepperoni pizza.",
    price: 1399,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12367", // Vegetarian Pizza
    desc: "Vegetarian Pizza",
    detailedDesc: "Pizza topped with a variety of fresh vegetables.",
    price: 1499,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12368", // Ribeye Steak
    desc: "Ribeye Steak",
    detailedDesc: "A tender and flavorful ribeye steak.",
    price: 1799,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12369", // Greek Salad
    desc: "Greek Salad",
    detailedDesc: "Salad with feta cheese, olives, tomatoes, and cucumbers.",
    price: 999,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12370", // Club Sandwich
    desc: "Club Sandwich",
    detailedDesc:
      "Triple-decker sandwich with turkey, bacon, lettuce, and tomato.",
    price: 1099,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12371", // Grilled Salmon
    desc: "Grilled Salmon",
    detailedDesc: "Perfectly grilled salmon fillet.",
    price: 1499,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },
  {
    item: "12372", // Lobster Tail
    desc: "Lobster Tail",
    detailedDesc: "Succulent broiled lobster tail.",
    price: 1699,
    modMaxSel: 0,
    modMinSel: 0,
    count: 17,
    selected: false,
    qty: 0,
    multiModLists: false,
    isVisible: true,
    modifiers: [],
  },

  {
    item: "12346",
    price: 1299,
    desc: "Classic pizza",
    detailedDesc: "A classic pizza with your choice of toppings.",
    modifierDesc: "Choose size, crust, and extra toppings",
    multiModLists: true,
    modMaxSel: 3,
    modMinSel: 1,
    count: 1,
    selected: false,
    qty: 1,
    isVisible: true,
    modifiers: [
      {
        item: genUuid(),
        price: 0,
        desc: "Pizza Size",
        multiModLists: false,
        modMaxSel: 1,
        modMinSel: 1,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 0,
            desc: "Small",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Medium",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Large",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
      {
        item: genUuid(),
        price: 0,
        desc: "Crust Type",
        multiModLists: false,
        modMaxSel: 1,
        modMinSel: 1,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 0,
            desc: "Thin Crust",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Thick Crust",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
      {
        item: genUuid(),
        price: 0,
        desc: "Extra Toppings",
        multiModLists: false,
        modMaxSel: 2,
        modMinSel: 0,
        count: 1,
        selected: false,
        qty: 0,
        isVisible: true,
        modifiers: [
          {
            item: genUuid(),
            price: 0,
            desc: "Pepperoni",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Mushrooms",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Olives",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Extra Cheese",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
          {
            item: genUuid(),
            price: 0,
            desc: "Pineapple",
            multiModLists: false,
            modMaxSel: 1,
            modMinSel: 0,
            count: 1,
            selected: false,
            qty: 0,
            isVisible: true,
            modifiers: [],
          },
        ],
      },
    ],
  },
  {
    item: "6fae7d04-d546-4d99-90f7-27b4e08b3857",
    price: 0,
    desc: "Monday Lunch",
    modifierDesc: "",
    multiModLists: false,
    modMaxSel: 0,
    modMinSel: 0,
    count: -1,
    selected: false,
    qty: 1,
    modifiers: [
      {
        item: "abcfb1c6-242a-4cb9-bf06-2a680c59d172",
        price: 1225,
        desc: "Original Philly Chicken Sandwich",
        modifierDesc: "",
        multiModLists: true,
        modMaxSel: 0,
        modMinSel: 0,
        count: -1,
        selected: false,
        qty: 1,
        modifiers: [
          {
            item: "d580190e-2b3b-4033-a985-fdca0bdda636",
            price: 0,
            desc: "Burger Side",
            modifierDesc: "Side",
            multiModLists: true,
            modMaxSel: 1,
            modMinSel: 1,
            count: -1,
            selected: false,
            qty: 1,
            modifiers: [
              {
                item: "7840c860-55c5-4d4b-89c5-52568e25956c",
                price: 0,
                desc: "No Side",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "6d3ac43c-7833-4e55-8c05-2dd81932fb74",
                price: 0,
                desc: "Chips",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "52a18821-af1e-4042-a6d9-2dbcee40cc4d",
                price: 0,
                desc: "French Fries",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [
                  {
                    item: "3a9f991f-b0e9-4257-8fde-14a7a9030ed5",
                    price: 50,
                    desc: "Ranch Dressing",
                    modifierDesc: "Ranch",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "414474b4-58dc-477b-b512-03687da6b4d4",
                    price: 0,
                    desc: "Scallions",
                    modifierDesc: null,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "71c31805-4265-4af9-b46b-5d4a4b82142e",
                    price: 50,
                    desc: "Bacon",
                    modifierDesc: "",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "c0c0bfba-690d-4de6-80b6-44336572238e",
                    price: 50,
                    desc: "Cheese Sauce",
                    modifierDesc: "",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                ],
                isVisible: true,
              },
              {
                item: "479bf4b6-edee-48e8-b567-f5972e2bf065",
                price: 0,
                desc: "Cup of Soup Upcharge",
                modifierDesc: "",
                multiModLists: true,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [
                  {
                    item: "d266e4d7-6bd3-470c-b10b-a13c4915995b",
                    price: 0,
                    desc: "Soup Selection",
                    modifierDesc: "Soup",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [
                      {
                        item: "81e10193-500e-4dad-8c90-f92c5608f0cc",
                        price: 0,
                        desc: "Chili",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "94bfb49e-0ab9-439e-b211-14038a804fb8",
                        price: 0,
                        desc: "Wedding",
                        modifierDesc: "Wedding",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "4bf15a75-24c6-4623-a635-04eefc829700",
                        price: 0,
                        desc: "Minestrone",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "4cec245b-3dcd-4bc9-b2d4-255715879145",
                        price: 0,
                        desc: "Soup du jour",
                        modifierDesc: "",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                    ],
                    isVisible: false,
                  },
                ],
                isVisible: false,
              },
              {
                item: "241e636a-e688-478f-abdf-10ade22686b7",
                price: 0,
                desc: "Bowl of Soup Upcharge",
                modifierDesc: "",
                multiModLists: true,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [
                  {
                    item: "d266e4d7-6bd3-470c-b10b-a13c4915995b",
                    price: 0,
                    desc: "Soup Selection",
                    modifierDesc: "Soup",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [
                      {
                        item: "81e10193-500e-4dad-8c90-f92c5608f0cc",
                        price: 0,
                        desc: "Chili",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "94bfb49e-0ab9-439e-b211-14038a804fb8",
                        price: 0,
                        desc: "Wedding",
                        modifierDesc: "Wedding",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "4bf15a75-24c6-4623-a635-04eefc829700",
                        price: 0,
                        desc: "Minestrone",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "4cec245b-3dcd-4bc9-b2d4-255715879145",
                        price: 0,
                        desc: "Soup du jour",
                        modifierDesc: "",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                    ],
                    isVisible: false,
                  },
                ],
                isVisible: false,
              },
            ],
            isVisible: false,
          },
          {
            item: "3cf77697-7c37-4e4b-b89a-9616ea452131",
            price: 0,
            desc: "Sandwich Prep",
            modifierDesc: "Prep",
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: -1,
            selected: false,
            qty: 1,
            modifiers: [
              {
                item: "be34ec60-fc09-44f9-b84b-8062708e91ae",
                price: 0,
                desc: "Prep - Raw Veggies",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "8df98efc-a1b8-4d94-a38a-fa31c9b3282f",
                price: 0,
                desc: "Prep - Cooked Veggies",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "e4cba804-b313-42f5-a674-9718ad5705d6",
                price: 0,
                desc: "Bun on Side",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "283b4edd-5ff9-42cd-a9e1-24892eb13ae2",
                price: 0,
                desc: "Cut in Half",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "27bc0e73-b553-4f8e-b707-3c70b9084db1",
                price: 0,
                desc: "Cut in Quarters",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "9ce5bd02-f60c-47be-9755-062fd54b36e0",
                price: 0,
                desc: "Toasted Bun",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "8f62796d-bc33-4257-8b03-46eb65c65d41",
                price: 0,
                desc: "Toasted Bun Light",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "28579820-3f1a-41a9-ab57-2f38edf69555",
                price: 0,
                desc: "Toasted Bun Dark",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "bf1fe4d0-cc05-40b4-8ccd-79113aa17e21",
                price: 0,
                desc: "Veg on Side",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "dd81cf4f-8bf5-42e6-b673-a39ac02b0382",
                price: 0,
                desc: "Prep - NoBun",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
            ],
            isVisible: false,
          },
          {
            item: "8aa3fdd9-c480-41b6-bf4e-4999d110cf95",
            price: 0,
            desc: "Sandwich Remove",
            modifierDesc: "No",
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: -1,
            selected: false,
            qty: 1,
            modifiers: [
              {
                item: "5452f0d8-2e6a-428d-bc9d-c0d8fb4960d1",
                price: 0,
                desc: "No - Croutons",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "66d802d2-23d4-41dd-a11e-f6daf578c0af",
                price: 0,
                desc: "Sauce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "20fed8af-ff61-4c11-94b8-124f05ace503",
                price: 0,
                desc: "Sauerkraut",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "12aa05a6-6e23-4c87-9a16-8798109a80bb",
                price: 0,
                desc: "Pickle",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "6d3ac43c-7833-4e55-8c05-2dd81932fb74",
                price: 0,
                desc: "Chips",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "31c2d64d-bda4-45ea-baea-9af866c564e6",
                price: 0,
                desc: "Fries",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "9f674a58-8e84-49b4-8c3b-45a972357935",
                price: 0,
                desc: "1000 Island",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "95bc597e-0de6-4376-85c7-9e0ae2920ee0",
                price: 0,
                desc: "Cocktail Sauce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "306fcd91-632d-4235-a1e6-f86ae6e2aa9b",
                price: 0,
                desc: "Ketchup",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "7f5e890d-986e-4011-b90a-4153b944d612",
                price: 0,
                desc: "Cheese",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "88e63a73-f9ac-4efc-89f1-931be3e5ebea",
                price: 0,
                desc: "Tartar Sauce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "29df4e63-3e9e-42c7-8354-9dda7022a332",
                price: 0,
                desc: "Lettuce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "8e0144e4-7941-41c6-aeda-af9c00fc6f00",
                price: 0,
                desc: "Honey Mustard",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "577d2a67-2a17-401f-b727-c4bb646bcbf3",
                price: 0,
                desc: "Onion",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "5d9e4872-d5b5-45a2-a345-554b4739a5ca",
                price: 0,
                desc: "Mustard",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "e5653e97-005f-42d8-b30d-d7048a02664d",
                price: 0,
                desc: "No - Italian Dressing",
                modifierDesc: "Italian",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "0f70909d-e99c-4138-b06e-4be6ce1f4d41",
                price: 0,
                desc: "Tomato",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "d4327700-0dcc-40a5-a32c-30e0587ba625",
                price: 0,
                desc: "Green Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "60839095-f5ce-463f-8b64-ea282bade54f",
                price: 0,
                desc: "Dressing",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "0e47eb91-4a92-4c0d-8ba9-92b4dbc51fc9",
                price: 0,
                desc: "Banana Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "7cca4222-513b-4f9c-81ff-ec5f51a99847",
                price: 0,
                desc: "Cucumbers",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "4e4bf891-d736-48a6-a8dc-1edd1a4cb34b",
                price: 0,
                desc: "Red Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "65c96669-f3ad-4d32-aa23-4a4bba5fc0aa",
                price: 0,
                desc: "Mayo",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "3f30024a-090d-46ac-ab84-7a9c7ae7dc0d",
                price: 0,
                desc: "Olives",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "c04943d9-be1f-4657-96aa-92d5ca5a8c5b",
                price: 0,
                desc: "Mushroom",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
            ],
            isVisible: false,
          },
          {
            item: "d8ca774a-79fe-4989-9623-5d3a888a7338",
            price: 0,
            desc: "Sandwich Toppings (NC)",
            modifierDesc: "With (NC)",
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: -1,
            selected: false,
            qty: 1,
            modifiers: [
              {
                item: "5452f0d8-2e6a-428d-bc9d-c0d8fb4960d1",
                price: 0,
                desc: "With (NC) - Croutons",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "20fed8af-ff61-4c11-94b8-124f05ace503",
                price: 0,
                desc: "Sauerkraut",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "12aa05a6-6e23-4c87-9a16-8798109a80bb",
                price: 0,
                desc: "Pickle",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "9f674a58-8e84-49b4-8c3b-45a972357935",
                price: 0,
                desc: "1000 Island",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "95bc597e-0de6-4376-85c7-9e0ae2920ee0",
                price: 0,
                desc: "Cocktail Sauce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "306fcd91-632d-4235-a1e6-f86ae6e2aa9b",
                price: 0,
                desc: "Ketchup",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "88e63a73-f9ac-4efc-89f1-931be3e5ebea",
                price: 0,
                desc: "Tartar Sauce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "29df4e63-3e9e-42c7-8354-9dda7022a332",
                price: 0,
                desc: "Lettuce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "577d2a67-2a17-401f-b727-c4bb646bcbf3",
                price: 0,
                desc: "Onion",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "5d9e4872-d5b5-45a2-a345-554b4739a5ca",
                price: 0,
                desc: "Mustard",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "0f70909d-e99c-4138-b06e-4be6ce1f4d41",
                price: 0,
                desc: "Tomato",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "d4327700-0dcc-40a5-a32c-30e0587ba625",
                price: 0,
                desc: "Green Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "0e47eb91-4a92-4c0d-8ba9-92b4dbc51fc9",
                price: 0,
                desc: "Banana Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "5c8eced9-3e07-4d0a-bc17-1472e420990c",
                price: 0,
                desc: "Grilled Onion",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "4e4bf891-d736-48a6-a8dc-1edd1a4cb34b",
                price: 0,
                desc: "Red Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "65c96669-f3ad-4d32-aa23-4a4bba5fc0aa",
                price: 0,
                desc: "Mayo",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "3f30024a-090d-46ac-ab84-7a9c7ae7dc0d",
                price: 0,
                desc: "Olives",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "c04943d9-be1f-4657-96aa-92d5ca5a8c5b",
                price: 0,
                desc: "Mushroom",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "b8540675-7fb9-41c6-8cde-548bd1c78125",
                price: 50,
                desc: "Egg W/ Cook",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [
                  {
                    item: "f32de985-9b57-4e58-b804-57a4bf84e7c7",
                    price: 0,
                    desc: "Egg",
                    modifierDesc: null,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "c8872395-f95a-4203-8644-9f68be320c8d",
                    price: 0,
                    desc: "Egg - Over Hard",
                    modifierDesc: "Over Hard",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "6e41fd21-545c-47a2-9b12-63f765c4e65e",
                    price: 0,
                    desc: "Egg - Over Light",
                    modifierDesc: "Over Light",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "ddb3010c-5189-4d9d-99d5-64776c36d94f",
                    price: 0,
                    desc: "Egg - Over Med",
                    modifierDesc: "Over Medium",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "f091fe25-1069-4f70-8e01-e30dac1a601f",
                    price: 0,
                    desc: "Egg - Over Well",
                    modifierDesc: "Over Well",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "6aef104b-eb40-43be-accd-fe2e9b7ce9bc",
                    price: 0,
                    desc: "Egg - Poached",
                    modifierDesc: "Poached",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "04a2191a-8f83-4424-acca-2dd56e2cc616",
                    price: 0,
                    desc: "Egg - Scram Lite",
                    modifierDesc: "Light Scrambled",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "4af6b8e4-a294-496d-be2c-481d35bc9262",
                    price: 0,
                    desc: "Egg - Scrambled",
                    modifierDesc: "Scrambled",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "db6afb74-9707-47a5-a70b-f00cf5f6383f",
                    price: 0,
                    desc: "Egg - Sunny Up",
                    modifierDesc: "Sunny Side Up",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "3b638f04-f34e-4884-ac67-7bdd88878eac",
                    price: 0,
                    desc: "Egg Cook",
                    modifierDesc: null,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [
                      {
                        item: "c8872395-f95a-4203-8644-9f68be320c8d",
                        price: 0,
                        desc: "Egg - Over Hard",
                        modifierDesc: "Over Hard",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "6e41fd21-545c-47a2-9b12-63f765c4e65e",
                        price: 0,
                        desc: "Egg - Over Light",
                        modifierDesc: "Over Light",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "ddb3010c-5189-4d9d-99d5-64776c36d94f",
                        price: 0,
                        desc: "Egg - Over Med",
                        modifierDesc: "Over Medium",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "f091fe25-1069-4f70-8e01-e30dac1a601f",
                        price: 0,
                        desc: "Egg - Over Well",
                        modifierDesc: "Over Well",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "6aef104b-eb40-43be-accd-fe2e9b7ce9bc",
                        price: 0,
                        desc: "Egg - Poached",
                        modifierDesc: "Poached",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "04a2191a-8f83-4424-acca-2dd56e2cc616",
                        price: 0,
                        desc: "Egg - Scram Lite",
                        modifierDesc: "Light Scrambled",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "4af6b8e4-a294-496d-be2c-481d35bc9262",
                        price: 0,
                        desc: "Egg - Scrambled",
                        modifierDesc: "Scrambled",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "db6afb74-9707-47a5-a70b-f00cf5f6383f",
                        price: 0,
                        desc: "Egg - Sunny Up",
                        modifierDesc: "Sunny Side Up",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                    ],
                    isVisible: false,
                  },
                ],
                isVisible: true,
              },
            ],
            isVisible: false,
          },
          {
            item: "64a7b871-a15b-476c-869c-303551056ed6",
            price: 0,
            desc: "Sandwich Additions",
            modifierDesc: "Add",
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: -1,
            selected: false,
            qty: 1,
            modifiers: [
              {
                item: "2b1d6f51-e2f9-4536-ba88-6af90d0879f3",
                price: 0,
                desc: "Add - A1 Steak Sauce",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "20fed8af-ff61-4c11-94b8-124f05ace503",
                price: 50,
                desc: "Sauerkraut",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "2d1ba3cb-8f94-413a-8e4d-966a40d7e1c0",
                price: 300,
                desc: "Grilled Chicken",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "eaf7d2a7-9e92-4472-a97f-173b14a512e9",
                price: 300,
                desc: "Add - Steak",
                modifierDesc: "Steak",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "12aa05a6-6e23-4c87-9a16-8798109a80bb",
                price: 0,
                desc: "Pickle",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "a9bad74c-f397-4a85-b7c9-be4f0db9109c",
                price: 125,
                desc: "Cappicola",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "46a5478a-3f18-4106-aa18-be32cdf17a22",
                price: 50,
                desc: "Mozzarella",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "cf370162-174a-42de-bb7e-303374a12c99",
                price: 50,
                desc: "Cheddar",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "13893df0-a980-4282-9ac5-079fdff2ab40",
                price: 50,
                desc: "Pepperjack",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "51ea6841-b580-49a4-99fb-97d1685c0876",
                price: 50,
                desc: "Swiss Cheese",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "95bc597e-0de6-4376-85c7-9e0ae2920ee0",
                price: 0,
                desc: "Cocktail Sauce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "306fcd91-632d-4235-a1e6-f86ae6e2aa9b",
                price: 0,
                desc: "Ketchup",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "7f5e890d-986e-4011-b90a-4153b944d612",
                price: 50,
                desc: "Cheese",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "88e63a73-f9ac-4efc-89f1-931be3e5ebea",
                price: 0,
                desc: "Tartar Sauce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "e6ff265d-d64a-444b-8a55-73ae99d9b9d4",
                price: 75,
                desc: "Feta",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "29df4e63-3e9e-42c7-8354-9dda7022a332",
                price: 0,
                desc: "Lettuce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "8e0144e4-7941-41c6-aeda-af9c00fc6f00",
                price: 0,
                desc: "Honey Mustard",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "577d2a67-2a17-401f-b727-c4bb646bcbf3",
                price: 0,
                desc: "Onion",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "5d9e4872-d5b5-45a2-a345-554b4739a5ca",
                price: 0,
                desc: "Mustard",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "63ff7398-427e-4696-ba43-76a4dc740b1e",
                price: 50,
                desc: "Provolone",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "a59ffb88-e1b2-4964-8547-2d6cd70a18c3",
                price: 150,
                desc: "Salami",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "0f70909d-e99c-4138-b06e-4be6ce1f4d41",
                price: 0,
                desc: "Tomato",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "d4327700-0dcc-40a5-a32c-30e0587ba625",
                price: 50,
                desc: "Green Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "f6af6263-1f15-4562-bc37-0cfdaaaa064a",
                price: 100,
                desc: "Ham",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "0e47eb91-4a92-4c0d-8ba9-92b4dbc51fc9",
                price: 50,
                desc: "Banana Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "670b2a35-e6a7-45c9-8750-6ec0e892c569",
                price: 50,
                desc: "American Cheese",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "5c8eced9-3e07-4d0a-bc17-1472e420990c",
                price: 50,
                desc: "Grilled Onion",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "4e4bf891-d736-48a6-a8dc-1edd1a4cb34b",
                price: 50,
                desc: "Red Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "65c96669-f3ad-4d32-aa23-4a4bba5fc0aa",
                price: 0,
                desc: "Mayo",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "3f30024a-090d-46ac-ab84-7a9c7ae7dc0d",
                price: 50,
                desc: "Olives",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "c04943d9-be1f-4657-96aa-92d5ca5a8c5b",
                price: 75,
                desc: "Mushroom",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "71c31805-4265-4af9-b46b-5d4a4b82142e",
                price: 100,
                desc: "Add - Bacon",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "e373e530-b9f6-469d-8eaf-1a35087240a9",
                price: 75,
                desc: "Add - Blue Cheese Crumbles",
                modifierDesc: "Blue Cheese Crumbles",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "b8540675-7fb9-41c6-8cde-548bd1c78125",
                price: 50,
                desc: "Egg W/ Cook",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [
                  {
                    item: "f32de985-9b57-4e58-b804-57a4bf84e7c7",
                    price: 0,
                    desc: "Egg",
                    modifierDesc: null,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "c8872395-f95a-4203-8644-9f68be320c8d",
                    price: 0,
                    desc: "Egg - Over Hard",
                    modifierDesc: "Over Hard",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "6e41fd21-545c-47a2-9b12-63f765c4e65e",
                    price: 0,
                    desc: "Egg - Over Light",
                    modifierDesc: "Over Light",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "ddb3010c-5189-4d9d-99d5-64776c36d94f",
                    price: 0,
                    desc: "Egg - Over Med",
                    modifierDesc: "Over Medium",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "f091fe25-1069-4f70-8e01-e30dac1a601f",
                    price: 0,
                    desc: "Egg - Over Well",
                    modifierDesc: "Over Well",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "6aef104b-eb40-43be-accd-fe2e9b7ce9bc",
                    price: 0,
                    desc: "Egg - Poached",
                    modifierDesc: "Poached",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "04a2191a-8f83-4424-acca-2dd56e2cc616",
                    price: 0,
                    desc: "Egg - Scram Lite",
                    modifierDesc: "Light Scrambled",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "4af6b8e4-a294-496d-be2c-481d35bc9262",
                    price: 0,
                    desc: "Egg - Scrambled",
                    modifierDesc: "Scrambled",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "db6afb74-9707-47a5-a70b-f00cf5f6383f",
                    price: 0,
                    desc: "Egg - Sunny Up",
                    modifierDesc: "Sunny Side Up",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "3b638f04-f34e-4884-ac67-7bdd88878eac",
                    price: 0,
                    desc: "Egg Cook",
                    modifierDesc: null,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [
                      {
                        item: "c8872395-f95a-4203-8644-9f68be320c8d",
                        price: 0,
                        desc: "Egg - Over Hard",
                        modifierDesc: "Over Hard",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "6e41fd21-545c-47a2-9b12-63f765c4e65e",
                        price: 0,
                        desc: "Egg - Over Light",
                        modifierDesc: "Over Light",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "ddb3010c-5189-4d9d-99d5-64776c36d94f",
                        price: 0,
                        desc: "Egg - Over Med",
                        modifierDesc: "Over Medium",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "f091fe25-1069-4f70-8e01-e30dac1a601f",
                        price: 0,
                        desc: "Egg - Over Well",
                        modifierDesc: "Over Well",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "6aef104b-eb40-43be-accd-fe2e9b7ce9bc",
                        price: 0,
                        desc: "Egg - Poached",
                        modifierDesc: "Poached",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "04a2191a-8f83-4424-acca-2dd56e2cc616",
                        price: 0,
                        desc: "Egg - Scram Lite",
                        modifierDesc: "Light Scrambled",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "4af6b8e4-a294-496d-be2c-481d35bc9262",
                        price: 0,
                        desc: "Egg - Scrambled",
                        modifierDesc: "Scrambled",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "db6afb74-9707-47a5-a70b-f00cf5f6383f",
                        price: 0,
                        desc: "Egg - Sunny Up",
                        modifierDesc: "Sunny Side Up",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                    ],
                    isVisible: false,
                  },
                ],
                isVisible: true,
              },
              {
                item: "bbc2b7c1-00bb-41b4-b299-10ee621dcd67",
                price: 300,
                desc: "Add - Meatballs",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [
                  {
                    item: "5e7f126b-f1ad-4379-9248-a4825da7b7f8",
                    price: 0,
                    desc: "Sauce Selection",
                    modifierDesc: "",
                    multiModLists: false,
                    modMaxSel: 1,
                    modMinSel: 1,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [
                      {
                        item: "66f4e7b9-3bc3-462a-b6ce-14d3f9d7b161",
                        price: 150,
                        desc: "Alfredo Sauce",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "0473dda4-e732-4637-a590-b5a77e244f6b",
                        price: 0,
                        desc: "Oil and Garlic",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "4fde3bef-a93f-4d3c-9f89-3298ce0bc97d",
                        price: 150,
                        desc: "Vodka Sauce",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "a1b236c0-f5e1-4504-939c-aef3d6da8778",
                        price: 0,
                        desc: "Tomato Sauce",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "f335fb97-33d2-4464-8bfc-d515b8ec030c",
                        price: 0,
                        desc: "Marinara Sauce",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "48dce982-7015-4440-a25f-9c0a769b59f1",
                        price: 100,
                        desc: "Cacciatore Sauce",
                        modifierDesc: "",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "a286bb60-0fea-4cf5-9260-751f28397efb",
                        price: 100,
                        desc: "Meat Sauce",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "1cb7f31d-a885-4aaf-83f5-e5777443d560",
                        price: 0,
                        desc: "Butter & Garlic",
                        modifierDesc: "",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                    ],
                    isVisible: false,
                  },
                ],
                isVisible: true,
              },
            ],
            isVisible: false,
          },
        ],
        isVisible: true,
      },
      {
        item: "454cbd20-05ed-40c0-ab88-1833eb16b8a6",
        price: 1150,
        desc: "Michaels Fish Sandwich",
        modifierDesc: "",
        multiModLists: true,
        modMaxSel: 0,
        modMinSel: 0,
        count: -1,
        selected: false,
        qty: 1,
        modifiers: [
          {
            item: "d580190e-2b3b-4033-a985-fdca0bdda636",
            price: 0,
            desc: "Burger Side",
            modifierDesc: "Side",
            multiModLists: true,
            modMaxSel: 1,
            modMinSel: 1,
            count: -1,
            selected: false,
            qty: 1,
            modifiers: [
              {
                item: "7840c860-55c5-4d4b-89c5-52568e25956c",
                price: 0,
                desc: "No Side",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "6d3ac43c-7833-4e55-8c05-2dd81932fb74",
                price: 0,
                desc: "Chips",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "52a18821-af1e-4042-a6d9-2dbcee40cc4d",
                price: 0,
                desc: "French Fries",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [
                  {
                    item: "3a9f991f-b0e9-4257-8fde-14a7a9030ed5",
                    price: 50,
                    desc: "Ranch Dressing",
                    modifierDesc: "Ranch",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "414474b4-58dc-477b-b512-03687da6b4d4",
                    price: 0,
                    desc: "Scallions",
                    modifierDesc: null,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "71c31805-4265-4af9-b46b-5d4a4b82142e",
                    price: 50,
                    desc: "Bacon",
                    modifierDesc: "",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "c0c0bfba-690d-4de6-80b6-44336572238e",
                    price: 50,
                    desc: "Cheese Sauce",
                    modifierDesc: "",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                ],
                isVisible: true,
              },
              {
                item: "479bf4b6-edee-48e8-b567-f5972e2bf065",
                price: 0,
                desc: "Cup of Soup Upcharge",
                modifierDesc: "",
                multiModLists: true,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [
                  {
                    item: "d266e4d7-6bd3-470c-b10b-a13c4915995b",
                    price: 0,
                    desc: "Soup Selection",
                    modifierDesc: "Soup",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [
                      {
                        item: "81e10193-500e-4dad-8c90-f92c5608f0cc",
                        price: 0,
                        desc: "Chili",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "94bfb49e-0ab9-439e-b211-14038a804fb8",
                        price: 0,
                        desc: "Wedding",
                        modifierDesc: "Wedding",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "4bf15a75-24c6-4623-a635-04eefc829700",
                        price: 0,
                        desc: "Minestrone",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "4cec245b-3dcd-4bc9-b2d4-255715879145",
                        price: 0,
                        desc: "Soup du jour",
                        modifierDesc: "",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                    ],
                    isVisible: false,
                  },
                ],
                isVisible: false,
              },
              {
                item: "241e636a-e688-478f-abdf-10ade22686b7",
                price: 0,
                desc: "Bowl of Soup Upcharge",
                modifierDesc: "",
                multiModLists: true,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [
                  {
                    item: "d266e4d7-6bd3-470c-b10b-a13c4915995b",
                    price: 0,
                    desc: "Soup Selection",
                    modifierDesc: "Soup",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [
                      {
                        item: "81e10193-500e-4dad-8c90-f92c5608f0cc",
                        price: 0,
                        desc: "Chili",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "94bfb49e-0ab9-439e-b211-14038a804fb8",
                        price: 0,
                        desc: "Wedding",
                        modifierDesc: "Wedding",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "4bf15a75-24c6-4623-a635-04eefc829700",
                        price: 0,
                        desc: "Minestrone",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "4cec245b-3dcd-4bc9-b2d4-255715879145",
                        price: 0,
                        desc: "Soup du jour",
                        modifierDesc: "",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                    ],
                    isVisible: false,
                  },
                ],
                isVisible: false,
              },
            ],
            isVisible: false,
          },
          {
            item: "3cf77697-7c37-4e4b-b89a-9616ea452131",
            price: 0,
            desc: "Sandwich Prep",
            modifierDesc: "Prep",
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: -1,
            selected: false,
            qty: 1,
            modifiers: [
              {
                item: "be34ec60-fc09-44f9-b84b-8062708e91ae",
                price: 0,
                desc: "Prep - Raw Veggies",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "8df98efc-a1b8-4d94-a38a-fa31c9b3282f",
                price: 0,
                desc: "Prep - Cooked Veggies",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "e4cba804-b313-42f5-a674-9718ad5705d6",
                price: 0,
                desc: "Bun on Side",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "283b4edd-5ff9-42cd-a9e1-24892eb13ae2",
                price: 0,
                desc: "Cut in Half",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "27bc0e73-b553-4f8e-b707-3c70b9084db1",
                price: 0,
                desc: "Cut in Quarters",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "9ce5bd02-f60c-47be-9755-062fd54b36e0",
                price: 0,
                desc: "Toasted Bun",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "8f62796d-bc33-4257-8b03-46eb65c65d41",
                price: 0,
                desc: "Toasted Bun Light",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "28579820-3f1a-41a9-ab57-2f38edf69555",
                price: 0,
                desc: "Toasted Bun Dark",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "bf1fe4d0-cc05-40b4-8ccd-79113aa17e21",
                price: 0,
                desc: "Veg on Side",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "dd81cf4f-8bf5-42e6-b673-a39ac02b0382",
                price: 0,
                desc: "Prep - NoBun",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
            ],
            isVisible: false,
          },
          {
            item: "8aa3fdd9-c480-41b6-bf4e-4999d110cf95",
            price: 0,
            desc: "Sandwich Remove",
            modifierDesc: "No",
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: -1,
            selected: false,
            qty: 1,
            modifiers: [
              {
                item: "5452f0d8-2e6a-428d-bc9d-c0d8fb4960d1",
                price: 0,
                desc: "No - Croutons",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "66d802d2-23d4-41dd-a11e-f6daf578c0af",
                price: 0,
                desc: "Sauce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "20fed8af-ff61-4c11-94b8-124f05ace503",
                price: 0,
                desc: "Sauerkraut",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "12aa05a6-6e23-4c87-9a16-8798109a80bb",
                price: 0,
                desc: "Pickle",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "6d3ac43c-7833-4e55-8c05-2dd81932fb74",
                price: 0,
                desc: "Chips",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "31c2d64d-bda4-45ea-baea-9af866c564e6",
                price: 0,
                desc: "Fries",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "9f674a58-8e84-49b4-8c3b-45a972357935",
                price: 0,
                desc: "1000 Island",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "95bc597e-0de6-4376-85c7-9e0ae2920ee0",
                price: 0,
                desc: "Cocktail Sauce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "306fcd91-632d-4235-a1e6-f86ae6e2aa9b",
                price: 0,
                desc: "Ketchup",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "7f5e890d-986e-4011-b90a-4153b944d612",
                price: 0,
                desc: "Cheese",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "88e63a73-f9ac-4efc-89f1-931be3e5ebea",
                price: 0,
                desc: "Tartar Sauce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "29df4e63-3e9e-42c7-8354-9dda7022a332",
                price: 0,
                desc: "Lettuce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "8e0144e4-7941-41c6-aeda-af9c00fc6f00",
                price: 0,
                desc: "Honey Mustard",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "577d2a67-2a17-401f-b727-c4bb646bcbf3",
                price: 0,
                desc: "Onion",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "5d9e4872-d5b5-45a2-a345-554b4739a5ca",
                price: 0,
                desc: "Mustard",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "e5653e97-005f-42d8-b30d-d7048a02664d",
                price: 0,
                desc: "No - Italian Dressing",
                modifierDesc: "Italian",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "0f70909d-e99c-4138-b06e-4be6ce1f4d41",
                price: 0,
                desc: "Tomato",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "d4327700-0dcc-40a5-a32c-30e0587ba625",
                price: 0,
                desc: "Green Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "60839095-f5ce-463f-8b64-ea282bade54f",
                price: 0,
                desc: "Dressing",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "0e47eb91-4a92-4c0d-8ba9-92b4dbc51fc9",
                price: 0,
                desc: "Banana Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "7cca4222-513b-4f9c-81ff-ec5f51a99847",
                price: 0,
                desc: "Cucumbers",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "4e4bf891-d736-48a6-a8dc-1edd1a4cb34b",
                price: 0,
                desc: "Red Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "65c96669-f3ad-4d32-aa23-4a4bba5fc0aa",
                price: 0,
                desc: "Mayo",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "3f30024a-090d-46ac-ab84-7a9c7ae7dc0d",
                price: 0,
                desc: "Olives",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "c04943d9-be1f-4657-96aa-92d5ca5a8c5b",
                price: 0,
                desc: "Mushroom",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
            ],
            isVisible: false,
          },
          {
            item: "d8ca774a-79fe-4989-9623-5d3a888a7338",
            price: 0,
            desc: "Sandwich Toppings (NC)",
            modifierDesc: "With (NC)",
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: -1,
            selected: false,
            qty: 1,
            modifiers: [
              {
                item: "5452f0d8-2e6a-428d-bc9d-c0d8fb4960d1",
                price: 0,
                desc: "With (NC) - Croutons",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "20fed8af-ff61-4c11-94b8-124f05ace503",
                price: 0,
                desc: "Sauerkraut",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "12aa05a6-6e23-4c87-9a16-8798109a80bb",
                price: 0,
                desc: "Pickle",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "9f674a58-8e84-49b4-8c3b-45a972357935",
                price: 0,
                desc: "1000 Island",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "95bc597e-0de6-4376-85c7-9e0ae2920ee0",
                price: 0,
                desc: "Cocktail Sauce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "306fcd91-632d-4235-a1e6-f86ae6e2aa9b",
                price: 0,
                desc: "Ketchup",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "88e63a73-f9ac-4efc-89f1-931be3e5ebea",
                price: 0,
                desc: "Tartar Sauce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "29df4e63-3e9e-42c7-8354-9dda7022a332",
                price: 0,
                desc: "Lettuce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "577d2a67-2a17-401f-b727-c4bb646bcbf3",
                price: 0,
                desc: "Onion",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "5d9e4872-d5b5-45a2-a345-554b4739a5ca",
                price: 0,
                desc: "Mustard",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "0f70909d-e99c-4138-b06e-4be6ce1f4d41",
                price: 0,
                desc: "Tomato",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "d4327700-0dcc-40a5-a32c-30e0587ba625",
                price: 0,
                desc: "Green Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "0e47eb91-4a92-4c0d-8ba9-92b4dbc51fc9",
                price: 0,
                desc: "Banana Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "5c8eced9-3e07-4d0a-bc17-1472e420990c",
                price: 0,
                desc: "Grilled Onion",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "4e4bf891-d736-48a6-a8dc-1edd1a4cb34b",
                price: 0,
                desc: "Red Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "65c96669-f3ad-4d32-aa23-4a4bba5fc0aa",
                price: 0,
                desc: "Mayo",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "3f30024a-090d-46ac-ab84-7a9c7ae7dc0d",
                price: 0,
                desc: "Olives",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "c04943d9-be1f-4657-96aa-92d5ca5a8c5b",
                price: 0,
                desc: "Mushroom",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "b8540675-7fb9-41c6-8cde-548bd1c78125",
                price: 50,
                desc: "Egg W/ Cook",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [
                  {
                    item: "f32de985-9b57-4e58-b804-57a4bf84e7c7",
                    price: 0,
                    desc: "Egg",
                    modifierDesc: null,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "c8872395-f95a-4203-8644-9f68be320c8d",
                    price: 0,
                    desc: "Egg - Over Hard",
                    modifierDesc: "Over Hard",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "6e41fd21-545c-47a2-9b12-63f765c4e65e",
                    price: 0,
                    desc: "Egg - Over Light",
                    modifierDesc: "Over Light",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "ddb3010c-5189-4d9d-99d5-64776c36d94f",
                    price: 0,
                    desc: "Egg - Over Med",
                    modifierDesc: "Over Medium",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "f091fe25-1069-4f70-8e01-e30dac1a601f",
                    price: 0,
                    desc: "Egg - Over Well",
                    modifierDesc: "Over Well",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "6aef104b-eb40-43be-accd-fe2e9b7ce9bc",
                    price: 0,
                    desc: "Egg - Poached",
                    modifierDesc: "Poached",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "04a2191a-8f83-4424-acca-2dd56e2cc616",
                    price: 0,
                    desc: "Egg - Scram Lite",
                    modifierDesc: "Light Scrambled",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "4af6b8e4-a294-496d-be2c-481d35bc9262",
                    price: 0,
                    desc: "Egg - Scrambled",
                    modifierDesc: "Scrambled",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "db6afb74-9707-47a5-a70b-f00cf5f6383f",
                    price: 0,
                    desc: "Egg - Sunny Up",
                    modifierDesc: "Sunny Side Up",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "3b638f04-f34e-4884-ac67-7bdd88878eac",
                    price: 0,
                    desc: "Egg Cook",
                    modifierDesc: null,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [
                      {
                        item: "c8872395-f95a-4203-8644-9f68be320c8d",
                        price: 0,
                        desc: "Egg - Over Hard",
                        modifierDesc: "Over Hard",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "6e41fd21-545c-47a2-9b12-63f765c4e65e",
                        price: 0,
                        desc: "Egg - Over Light",
                        modifierDesc: "Over Light",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "ddb3010c-5189-4d9d-99d5-64776c36d94f",
                        price: 0,
                        desc: "Egg - Over Med",
                        modifierDesc: "Over Medium",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "f091fe25-1069-4f70-8e01-e30dac1a601f",
                        price: 0,
                        desc: "Egg - Over Well",
                        modifierDesc: "Over Well",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "6aef104b-eb40-43be-accd-fe2e9b7ce9bc",
                        price: 0,
                        desc: "Egg - Poached",
                        modifierDesc: "Poached",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "04a2191a-8f83-4424-acca-2dd56e2cc616",
                        price: 0,
                        desc: "Egg - Scram Lite",
                        modifierDesc: "Light Scrambled",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "4af6b8e4-a294-496d-be2c-481d35bc9262",
                        price: 0,
                        desc: "Egg - Scrambled",
                        modifierDesc: "Scrambled",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "db6afb74-9707-47a5-a70b-f00cf5f6383f",
                        price: 0,
                        desc: "Egg - Sunny Up",
                        modifierDesc: "Sunny Side Up",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                    ],
                    isVisible: false,
                  },
                ],
                isVisible: true,
              },
            ],
            isVisible: false,
          },
          {
            item: "64a7b871-a15b-476c-869c-303551056ed6",
            price: 0,
            desc: "Sandwich Additions",
            modifierDesc: "Add",
            multiModLists: false,
            modMaxSel: 0,
            modMinSel: 0,
            count: -1,
            selected: false,
            qty: 1,
            modifiers: [
              {
                item: "2b1d6f51-e2f9-4536-ba88-6af90d0879f3",
                price: 0,
                desc: "Add - A1 Steak Sauce",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "20fed8af-ff61-4c11-94b8-124f05ace503",
                price: 50,
                desc: "Sauerkraut",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "2d1ba3cb-8f94-413a-8e4d-966a40d7e1c0",
                price: 300,
                desc: "Grilled Chicken",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "eaf7d2a7-9e92-4472-a97f-173b14a512e9",
                price: 300,
                desc: "Add - Steak",
                modifierDesc: "Steak",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "12aa05a6-6e23-4c87-9a16-8798109a80bb",
                price: 0,
                desc: "Pickle",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "a9bad74c-f397-4a85-b7c9-be4f0db9109c",
                price: 125,
                desc: "Cappicola",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "46a5478a-3f18-4106-aa18-be32cdf17a22",
                price: 50,
                desc: "Mozzarella",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "cf370162-174a-42de-bb7e-303374a12c99",
                price: 50,
                desc: "Cheddar",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "13893df0-a980-4282-9ac5-079fdff2ab40",
                price: 50,
                desc: "Pepperjack",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "51ea6841-b580-49a4-99fb-97d1685c0876",
                price: 50,
                desc: "Swiss Cheese",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "95bc597e-0de6-4376-85c7-9e0ae2920ee0",
                price: 0,
                desc: "Cocktail Sauce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "306fcd91-632d-4235-a1e6-f86ae6e2aa9b",
                price: 0,
                desc: "Ketchup",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "7f5e890d-986e-4011-b90a-4153b944d612",
                price: 50,
                desc: "Cheese",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "88e63a73-f9ac-4efc-89f1-931be3e5ebea",
                price: 0,
                desc: "Tartar Sauce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "e6ff265d-d64a-444b-8a55-73ae99d9b9d4",
                price: 75,
                desc: "Feta",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "29df4e63-3e9e-42c7-8354-9dda7022a332",
                price: 0,
                desc: "Lettuce",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "8e0144e4-7941-41c6-aeda-af9c00fc6f00",
                price: 0,
                desc: "Honey Mustard",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "577d2a67-2a17-401f-b727-c4bb646bcbf3",
                price: 0,
                desc: "Onion",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "5d9e4872-d5b5-45a2-a345-554b4739a5ca",
                price: 0,
                desc: "Mustard",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "63ff7398-427e-4696-ba43-76a4dc740b1e",
                price: 50,
                desc: "Provolone",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "a59ffb88-e1b2-4964-8547-2d6cd70a18c3",
                price: 150,
                desc: "Salami",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "0f70909d-e99c-4138-b06e-4be6ce1f4d41",
                price: 0,
                desc: "Tomato",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "d4327700-0dcc-40a5-a32c-30e0587ba625",
                price: 50,
                desc: "Green Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "f6af6263-1f15-4562-bc37-0cfdaaaa064a",
                price: 100,
                desc: "Ham",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "0e47eb91-4a92-4c0d-8ba9-92b4dbc51fc9",
                price: 50,
                desc: "Banana Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "670b2a35-e6a7-45c9-8750-6ec0e892c569",
                price: 50,
                desc: "American Cheese",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "5c8eced9-3e07-4d0a-bc17-1472e420990c",
                price: 50,
                desc: "Grilled Onion",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "4e4bf891-d736-48a6-a8dc-1edd1a4cb34b",
                price: 50,
                desc: "Red Pepper",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "65c96669-f3ad-4d32-aa23-4a4bba5fc0aa",
                price: 0,
                desc: "Mayo",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "3f30024a-090d-46ac-ab84-7a9c7ae7dc0d",
                price: 50,
                desc: "Olives",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "c04943d9-be1f-4657-96aa-92d5ca5a8c5b",
                price: 75,
                desc: "Mushroom",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "71c31805-4265-4af9-b46b-5d4a4b82142e",
                price: 100,
                desc: "Add - Bacon",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "e373e530-b9f6-469d-8eaf-1a35087240a9",
                price: 75,
                desc: "Add - Blue Cheese Crumbles",
                modifierDesc: "Blue Cheese Crumbles",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [],
                isVisible: true,
              },
              {
                item: "b8540675-7fb9-41c6-8cde-548bd1c78125",
                price: 50,
                desc: "Egg W/ Cook",
                modifierDesc: null,
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [
                  {
                    item: "f32de985-9b57-4e58-b804-57a4bf84e7c7",
                    price: 0,
                    desc: "Egg",
                    modifierDesc: null,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "c8872395-f95a-4203-8644-9f68be320c8d",
                    price: 0,
                    desc: "Egg - Over Hard",
                    modifierDesc: "Over Hard",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "6e41fd21-545c-47a2-9b12-63f765c4e65e",
                    price: 0,
                    desc: "Egg - Over Light",
                    modifierDesc: "Over Light",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "ddb3010c-5189-4d9d-99d5-64776c36d94f",
                    price: 0,
                    desc: "Egg - Over Med",
                    modifierDesc: "Over Medium",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "f091fe25-1069-4f70-8e01-e30dac1a601f",
                    price: 0,
                    desc: "Egg - Over Well",
                    modifierDesc: "Over Well",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "6aef104b-eb40-43be-accd-fe2e9b7ce9bc",
                    price: 0,
                    desc: "Egg - Poached",
                    modifierDesc: "Poached",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "04a2191a-8f83-4424-acca-2dd56e2cc616",
                    price: 0,
                    desc: "Egg - Scram Lite",
                    modifierDesc: "Light Scrambled",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "4af6b8e4-a294-496d-be2c-481d35bc9262",
                    price: 0,
                    desc: "Egg - Scrambled",
                    modifierDesc: "Scrambled",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "db6afb74-9707-47a5-a70b-f00cf5f6383f",
                    price: 0,
                    desc: "Egg - Sunny Up",
                    modifierDesc: "Sunny Side Up",
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [],
                    isVisible: true,
                  },
                  {
                    item: "3b638f04-f34e-4884-ac67-7bdd88878eac",
                    price: 0,
                    desc: "Egg Cook",
                    modifierDesc: null,
                    multiModLists: false,
                    modMaxSel: 0,
                    modMinSel: 0,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [
                      {
                        item: "c8872395-f95a-4203-8644-9f68be320c8d",
                        price: 0,
                        desc: "Egg - Over Hard",
                        modifierDesc: "Over Hard",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "6e41fd21-545c-47a2-9b12-63f765c4e65e",
                        price: 0,
                        desc: "Egg - Over Light",
                        modifierDesc: "Over Light",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "ddb3010c-5189-4d9d-99d5-64776c36d94f",
                        price: 0,
                        desc: "Egg - Over Med",
                        modifierDesc: "Over Medium",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "f091fe25-1069-4f70-8e01-e30dac1a601f",
                        price: 0,
                        desc: "Egg - Over Well",
                        modifierDesc: "Over Well",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "6aef104b-eb40-43be-accd-fe2e9b7ce9bc",
                        price: 0,
                        desc: "Egg - Poached",
                        modifierDesc: "Poached",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "04a2191a-8f83-4424-acca-2dd56e2cc616",
                        price: 0,
                        desc: "Egg - Scram Lite",
                        modifierDesc: "Light Scrambled",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "4af6b8e4-a294-496d-be2c-481d35bc9262",
                        price: 0,
                        desc: "Egg - Scrambled",
                        modifierDesc: "Scrambled",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "db6afb74-9707-47a5-a70b-f00cf5f6383f",
                        price: 0,
                        desc: "Egg - Sunny Up",
                        modifierDesc: "Sunny Side Up",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                    ],
                    isVisible: false,
                  },
                ],
                isVisible: true,
              },
              {
                item: "bbc2b7c1-00bb-41b4-b299-10ee621dcd67",
                price: 300,
                desc: "Add - Meatballs",
                modifierDesc: "",
                multiModLists: false,
                modMaxSel: 0,
                modMinSel: 0,
                count: -1,
                selected: false,
                qty: 1,
                modifiers: [
                  {
                    item: "5e7f126b-f1ad-4379-9248-a4825da7b7f8",
                    price: 0,
                    desc: "Sauce Selection",
                    modifierDesc: "",
                    multiModLists: false,
                    modMaxSel: 1,
                    modMinSel: 1,
                    count: -1,
                    selected: false,
                    qty: 1,
                    modifiers: [
                      {
                        item: "66f4e7b9-3bc3-462a-b6ce-14d3f9d7b161",
                        price: 150,
                        desc: "Alfredo Sauce",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "0473dda4-e732-4637-a590-b5a77e244f6b",
                        price: 0,
                        desc: "Oil and Garlic",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "4fde3bef-a93f-4d3c-9f89-3298ce0bc97d",
                        price: 150,
                        desc: "Vodka Sauce",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "a1b236c0-f5e1-4504-939c-aef3d6da8778",
                        price: 0,
                        desc: "Tomato Sauce",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "f335fb97-33d2-4464-8bfc-d515b8ec030c",
                        price: 0,
                        desc: "Marinara Sauce",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "48dce982-7015-4440-a25f-9c0a769b59f1",
                        price: 100,
                        desc: "Cacciatore Sauce",
                        modifierDesc: "",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "a286bb60-0fea-4cf5-9260-751f28397efb",
                        price: 100,
                        desc: "Meat Sauce",
                        modifierDesc: null,
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                      {
                        item: "1cb7f31d-a885-4aaf-83f5-e5777443d560",
                        price: 0,
                        desc: "Butter & Garlic",
                        modifierDesc: "",
                        multiModLists: false,
                        modMaxSel: 0,
                        modMinSel: 0,
                        count: -1,
                        selected: false,
                        qty: 1,
                        modifiers: [],
                        isVisible: true,
                      },
                    ],
                    isVisible: false,
                  },
                ],
                isVisible: true,
              },
            ],
            isVisible: false,
          },
        ],
        isVisible: true,
      },
    ],
    isVisible: true,
  },
];
